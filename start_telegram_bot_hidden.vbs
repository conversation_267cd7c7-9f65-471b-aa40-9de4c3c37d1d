' Telegram Bot Hidden Startup Script
' This VBS script runs the Flask app completely hidden (no console window)
' Perfect for Windows startup automation

Dim objShell
Set objShell = CreateObject("WScript.Shell")

' Change to project directory and run the Python script in virtual environment
' The 0 parameter makes the window completely hidden
' The False parameter means don't wait for the process to complete
objShell.Run """C:\code\AI_Agent_Project_Improved\Question_Page\venv\Scripts\python.exe"" ""C:\code\AI_Agent_Project_Improved\Question_Page\page.py""", 0, False

' Clean up
Set objShell = Nothing
