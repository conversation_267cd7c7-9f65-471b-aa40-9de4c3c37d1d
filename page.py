from flask import Flask, render_template, request, jsonify, Response, redirect, url_for, session
import requests
import os
from groq import Groq
import tempfile
import logging
from io import BytesIO
import threading
import time
from collections import deque
import json
import queue
import re
import requests
import time
from urllib.parse import urlparse, parse_qs
import asyncio
import subprocess
import datetime
from pathlib import Path

# Try to import local config, fall back to default config
try:
    from config_local import *
except ImportError:
    from config import *

# Configure logging first
logging.basicConfig(level=getattr(logging, LOG_LEVEL))
logger = logging.getLogger(__name__)

# SSL Certificate Management Functions
def check_certificate_validity(cert_path):
    """Check if SSL certificate is valid and not expired."""
    try:
        if not os.path.exists(cert_path):
            return False, "Certificate file not found"

        # Check certificate expiration
        result = subprocess.run([
            'openssl', 'x509', '-in', cert_path, '-noout', '-dates'
        ], capture_output=True, text=True, timeout=10)

        if result.returncode != 0:
            return False, f"Failed to read certificate: {result.stderr}"

        # Parse the dates
        for line in result.stdout.split('\n'):
            if 'notAfter=' in line:
                date_str = line.split('notAfter=')[1].strip()
                # Parse the date format: Aug  9 03:30:45 2026 GMT
                try:
                    expiry_date = datetime.datetime.strptime(date_str, '%b %d %H:%M:%S %Y %Z')
                    current_date = datetime.datetime.utcnow()

                    # Check if certificate expires within 30 days
                    days_until_expiry = (expiry_date - current_date).days

                    if days_until_expiry <= 0:
                        return False, f"Certificate expired on {expiry_date}"
                    elif days_until_expiry <= 30:
                        return False, f"Certificate expires in {days_until_expiry} days"
                    else:
                        return True, f"Certificate valid until {expiry_date}"
                except ValueError as e:
                    return False, f"Failed to parse expiry date: {e}"

        return False, "Could not find expiry date in certificate"

    except subprocess.TimeoutExpired:
        return False, "Certificate check timed out"
    except Exception as e:
        return False, f"Error checking certificate: {e}"

def generate_ssl_certificate():
    """Generate a new self-signed SSL certificate."""
    try:
        cert_dir = Path('certs')
        cert_dir.mkdir(exist_ok=True)

        cert_path = cert_dir / 'cert.pem'
        key_path = cert_dir / 'key.pem'
        config_path = cert_dir / 'openssl.cnf'

        # Create OpenSSL config file with multiple IP addresses and hostnames
        config_content = f"""[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no
default_bits = 2048

[req_distinguished_name]
C = US
ST = California
L = San Francisco
O = Local Development
OU = IT Department
CN = ************

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth, clientAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
DNS.3 = host.docker.internal
IP.1 = 127.0.0.1
IP.2 = ::1
IP.3 = ***************
IP.4 = ************
"""

        # Write config file
        with open(config_path, 'w') as f:
            f.write(config_content)

        # Generate private key with stronger parameters
        key_cmd = [
            'openssl', 'genrsa', '-out', str(key_path), '4096'
        ]

        result = subprocess.run(key_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            raise Exception(f"Failed to generate private key: {result.stderr}")

        # Generate certificate with SHA256
        cert_cmd = [
            'openssl', 'req', '-new', '-x509', '-key', str(key_path),
            '-out', str(cert_path), '-days', '365', '-sha256',
            '-config', str(config_path), '-extensions', 'v3_req'
        ]

        result = subprocess.run(cert_cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            raise Exception(f"Failed to generate certificate: {result.stderr}")

        logger.info(f"Successfully generated new SSL certificate: {cert_path}")
        return True, f"Certificate generated successfully"

    except subprocess.TimeoutExpired:
        return False, "Certificate generation timed out"
    except Exception as e:
        logger.error(f"Error generating SSL certificate: {e}")
        return False, f"Failed to generate certificate: {e}"

def ensure_ssl_certificate():
    """Ensure SSL certificate exists and is valid, regenerate if needed."""
    try:
        cert_path = 'certs/cert.pem'
        key_path = 'certs/key.pem'

        # Check if certificate exists and is valid
        is_valid, message = check_certificate_validity(cert_path)

        if not is_valid:
            logger.warning(f"SSL certificate issue: {message}")
            logger.info("Regenerating SSL certificate...")

            success, gen_message = generate_ssl_certificate()
            if success:
                logger.info(f"SSL certificate regenerated: {gen_message}")
                return True
            else:
                logger.error(f"Failed to regenerate certificate: {gen_message}")
                return False
        else:
            logger.info(f"SSL certificate is valid: {message}")
            return True

    except Exception as e:
        logger.error(f"Error in SSL certificate management: {e}")
        return False

# Import Telegram bot functionality
try:
    from telegram_bot import get_telegram_processor, start_telegram_bot
    TELEGRAM_BOT_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Telegram bot not available: {e}")
    TELEGRAM_BOT_AVAILABLE = False

# Removed YouTube transcript functionality

# Initialize Flask app
app = Flask(__name__)
# Set static file caching to improve reload performance
try:
    app.config['SEND_FILE_MAX_AGE_DEFAULT'] = STATIC_MAX_AGE
except NameError:
    app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 3600

app.config['SECRET_KEY'] = SECRET_KEY

# Global storage for AI responses - using queue for real-time delivery
ai_response_queues = {}  # Dictionary to store queues for each client
response_lock = threading.Lock()

# Initialize Groq client
groq_client = None

if GROQ_API_KEY:
    try:
        # Try to initialize Groq client
        groq_client = Groq(api_key=GROQ_API_KEY)
        logger.info("Groq client initialized successfully")
    except TypeError as e:
        if "proxies" in str(e):
            logger.error("Groq package compatibility issue detected.")
            logger.error("Please run: pip uninstall groq httpx httpcore -y && pip install groq==0.8.0 httpx==0.27.0")
        else:
            logger.error(f"Failed to initialize Groq client: {e}")
        groq_client = None
    except Exception as e:
        logger.error(f"Failed to initialize Groq client: {e}")
        logger.error("Please check your Groq package version and dependencies")
        groq_client = None
# Lightweight in-memory cache for sources to reduce startup delay
SOURCES_CACHE_TTL = int(os.getenv('SOURCES_CACHE_TTL', '60'))  # seconds
_sources_cache = { 'list': { 'data': None, 'ts': 0 }, 'detailed': { 'data': None, 'ts': 0 } }

def _cache_get(key):
    now = time.time()
    entry = _sources_cache.get(key, {})
    if entry and entry.get('data') is not None and (now - entry.get('ts', 0) < SOURCES_CACHE_TTL):
        return entry['data']
    return None

def _cache_set(key, value):
    _sources_cache[key] = { 'data': value, 'ts': time.time() }

def _cache_clear():
    for k in _sources_cache.keys():
        _sources_cache[k] = { 'data': None, 'ts': 0 }


# Simple login system
@app.route('/login', methods=['GET', 'POST'])
def login():
    # Load config with fallbacks
    try:
        require_login = REQUIRE_LOGIN
        valid_username = LOGIN_USERNAME
        valid_password = LOGIN_PASSWORD
        login_users = LOGIN_USERS if 'LOGIN_USERS' in globals() else None
    except NameError:
        require_login = False
        valid_username = 'admin'
        valid_password = 'admin123'
        login_users = None

    if request.method == 'POST':
        username = request.form.get('username', '')
        password = request.form.get('password', '')

        # Support multiple users if configured
        is_valid = False
        if isinstance(login_users, dict):
            is_valid = login_users.get(username) == password
        else:
            is_valid = (username == valid_username and password == valid_password)

        if is_valid:
            session['logged_in'] = True
            session['username'] = username
            # Session period: default is non-permanent (clears on browser close)
            # Can be changed by setting session.permanent = True and PERMANENT_SESSION_LIFETIME
            next_url = request.args.get('next') or url_for('index')
            return redirect(next_url)
        else:
            return render_template('login.html', error='Invalid credentials')

    # If already logged in, go to index
    if session.get('logged_in'):
        return redirect(url_for('index'))

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/')
def index():
    """Serve the main question page."""
    try:
        if REQUIRE_LOGIN and not session.get('logged_in'):
            return redirect(url_for('login', next=request.path))
    except NameError:
        # In case config doesn't define REQUIRE_LOGIN
        pass
    return render_template('index.html')

@app.route('/api/sources')
def get_sources():
    """Get unique source filenames from ChromaDB for autocomplete."""
    try:
        # Serve from cache if fresh
        cached = _cache_get('list')
        if cached is not None:
            return jsonify({"sources": cached})

        # Call the existing ChromaDB app to get all documents
        response = requests.get(f"{CHROMADB_BASE_URL}/api/documents", timeout=5)
        if response.status_code != 200:
            logger.error(f"Failed to fetch documents from ChromaDB: {response.status_code}")
            return jsonify({"sources": _cache_get('list') or []})

        data = response.json() or {}
        documents = data.get('documents', [])

        # Extract unique source filenames
        sources = set()
        for doc in documents or []:
            if not doc:
                continue
            metadata = doc.get('metadata') or {}
            source = metadata.get('Source')
            if source:
                sources.add(source)

        sources_list = sorted(list(sources))
        _cache_set('list', sources_list)
        return jsonify({"sources": sources_list})

    except Exception as e:
        logger.error(f"/api/sources error: {e}")
        # Return stale cache if available
        return jsonify({"sources": _cache_get('list') or []})

@app.route('/api/sources/detailed')
def get_detailed_sources():
    """Get detailed source information for the sources panel."""
    try:
        cached = _cache_get('detailed')
        if cached is not None:
            return jsonify({"sources": cached})

        response = requests.get(f"{CHROMADB_BASE_URL}/api/documents", timeout=5)
        if response.status_code != 200:
            return jsonify({"sources": _cache_get('detailed') or []})

        data = response.json() or {}
        documents = data.get('documents', [])

        sources_map = {}
        for doc in documents or []:
            if not doc:
                continue
            metadata = doc.get('metadata') or {}
            source = metadata.get('Source')
            if source:
                if source not in sources_map:
                    source_type = 'doc'
                    ls = source.lower()
                    if ls.endswith('.pdf'):
                        source_type = 'pdf'
                    elif 'youtube' in ls or 'youtu.be' in ls or ls.endswith('.yt'):
                        source_type = 'youtube'
                    elif ls.startswith('pasted') or 'text' in ls:
                        source_type = 'text'
                    sources_map[source] = { 'id': source, 'title': source, 'display_title': (source[:-3] if source.lower().endswith('.yt') else source), 'type': source_type, 'document_count': 0 }
                sources_map[source]['document_count'] += 1

        sources_list = list(sources_map.values())
        sources_list.sort(key=lambda x: x['title'])
        _cache_set('detailed', sources_list)
        return jsonify({"sources": sources_list})

    except Exception as e:
        logger.error(f"/api/sources/detailed error: {e}")
        return jsonify({"sources": _cache_get('detailed') or []})

@app.route('/api/sources/add', methods=['POST'])
def add_source():
    """Add a new source manually."""
    try:
        data = request.get_json()
        if not data or 'title' not in data:
            return jsonify({"error": "No title provided"}), 400

        title = data['title'].strip()
        source_type = data.get('type', 'doc')

        if not title:
            return jsonify({"error": "Title cannot be empty"}), 400

        # For now, we'll just return success since we're not actually storing custom sources
        # In a real implementation, you might want to store these in a database
        new_source = {
            'id': f"custom_{int(time.time())}",
            'title': title,
            'type': source_type,
            'document_count': 0,
            'custom': True
        }

        logger.info(f"Added custom source: {title}")
        return jsonify({"status": "success", "source": new_source})

    except Exception as e:
        logger.error(f"Error adding source: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/sources/delete', methods=['DELETE'])
def delete_source():
    """Delete a source and all its associated chunks from ChromaDB via HTTP request."""
    try:
        data = request.get_json()

        if not data or 'source_id' not in data:
            return jsonify({"error": "No source_id provided"}), 400

        source_id = data['source_id'].strip()

        if not source_id:
            return jsonify({"error": "source_id cannot be empty"}), 400

        logger.info(f"Attempting to delete source: {source_id}")

        # Call the ChromaDB service to delete the source
        delete_url = f"{CHROMADB_BASE_URL}/api/documents/by-source"
        delete_payload = {"source": source_id}

        logger.info(f"Sending delete request to: {delete_url}")
        response = requests.delete(delete_url, json=delete_payload, timeout=30)

        if response.status_code == 200:
            result = response.json()
            deleted_count = result.get('deleted_count', 0)

            logger.info(f"Successfully deleted {deleted_count} chunks for source: {source_id}")

            return jsonify({
                "message": f"Successfully deleted source '{source_id}'",
                "deleted_chunks": deleted_count,
                "source_id": source_id
            })
        else:
            error_msg = "Unknown error"
            try:
                error_data = response.json()
                error_msg = error_data.get('error', f"HTTP {response.status_code}")
            except:
                error_msg = f"HTTP {response.status_code}: {response.text}"

            logger.error(f"Failed to delete source {source_id}: {error_msg}")
            return jsonify({"error": f"Failed to delete source: {error_msg}"}), response.status_code

    except requests.exceptions.RequestException as e:
        logger.error(f"Network error while deleting source {source_id}: {e}")
        return jsonify({"error": "Failed to connect to database service"}), 500
    except Exception as e:
        logger.error(f"Error deleting source: {e}")
        return jsonify({"error": "Internal server error"}), 500

# Removed YouTube test endpoint

def parse_subtitle_content(content, format_type):
    """Parse subtitle content and extract text with timestamps."""
    import xml.etree.ElementTree as ET

    transcript_list = []

    try:
        if format_type in ['vtt', 'webvtt']:
            # Parse WebVTT format
            lines = content.split('\n')
            current_entry = {}

            for line in lines:
                line = line.strip()
                if not line or line.startswith('WEBVTT') or line.startswith('NOTE'):
                    continue

                # Check if line contains timestamp
                if '-->' in line:
                    # Parse timestamp line like "00:00:01.000 --> 00:00:04.000"
                    times = line.split(' --> ')
                    if len(times) == 2:
                        start_time = parse_vtt_time(times[0])
                        current_entry = {'start': start_time, 'text': ''}
                elif current_entry and line:
                    # This is subtitle text
                    if current_entry.get('text'):
                        current_entry['text'] += ' ' + line
                    else:
                        current_entry['text'] = line

                    # If we have both start time and text, add to list
                    if 'start' in current_entry and current_entry['text']:
                        transcript_list.append(current_entry)
                        current_entry = {}

        elif format_type in ['srv3', 'ttml']:
            # Parse XML-based formats (srv3, ttml)
            root = ET.fromstring(content)

            # Find all text elements with timing
            for elem in root.iter():
                if elem.text and elem.text.strip():
                    start_time = 0

                    # Try to extract start time from various attributes
                    if 't' in elem.attrib:
                        start_time = float(elem.attrib['t']) / 1000.0  # Convert ms to seconds
                    elif 'begin' in elem.attrib:
                        start_time = parse_time_attribute(elem.attrib['begin'])

                    transcript_list.append({
                        'start': start_time,
                        'text': elem.text.strip()
                    })

        else:
            # Fallback: try to extract any text content
            lines = content.split('\n')
            for i, line in enumerate(lines):
                line = line.strip()
                if line and not line.startswith('<') and '-->' not in line:
                    transcript_list.append({
                        'start': i * 2.0,  # Approximate timing
                        'text': line
                    })

    except Exception as e:
        logger.warning(f"Error parsing subtitle content: {e}")
        # Fallback: extract any text content
        lines = content.split('\n')
        for i, line in enumerate(lines):
            line = line.strip()
            if line and not line.startswith('<') and '-->' not in line and 'WEBVTT' not in line:
                transcript_list.append({
                    'start': i * 2.0,
                    'text': line
                })

    return transcript_list

def parse_vtt_time(time_str):
    """Parse VTT time format to seconds."""
    try:
        # Format: HH:MM:SS.mmm or MM:SS.mmm
        parts = time_str.split(':')
        if len(parts) == 3:
            hours, minutes, seconds = parts
            return float(hours) * 3600 + float(minutes) * 60 + float(seconds)
        elif len(parts) == 2:
            minutes, seconds = parts
            return float(minutes) * 60 + float(seconds)
        else:
            return 0.0
    except:
        return 0.0

def parse_time_attribute(time_str):
    """Parse time attribute from XML formats."""
    try:
        # Handle various time formats
        if 's' in time_str:
            return float(time_str.replace('s', ''))
        elif ':' in time_str:
            return parse_vtt_time(time_str)
        else:
            return float(time_str)
    except:
        return 0.0

def broadcast_upload_status(filename, status, message=None, error=None):
    """Helper function to broadcast upload status to SSE clients."""
    try:
        payload = {
            "type": "upload_status",
            "endpoint_type": "youtube",
            "status": status,
            "filename": filename,
            "message": message,
            "error": error,
            "timestamp": time.time()
        }

        # Store status
        upload_status_map[filename] = payload

        # Broadcast to all connected SSE clients
        with response_lock:
            for client_id, client_queue in ai_response_queues.items():
                try:
                    client_queue.put_nowait(payload)
                except queue.Full:
                    logger.warning(f"Upload status queue full for client {client_id}")
    except Exception as e:
        logger.error(f"Error broadcasting upload status: {e}")

@app.route('/api/extract-youtube-transcript', methods=['POST'])
def extract_youtube_transcript():
    """Extract transcript from YouTube video and upload to ChromaDB."""
    temp_filename = None
    try:
        logger.info("YouTube transcript extraction endpoint called")

        data = request.get_json()
        if not data or 'youtube_url' not in data:
            return jsonify({"error": "No YouTube URL provided"}), 400

        youtube_url = data['youtube_url'].strip()

        # Extract video ID using the sample code logic
        video_id = extract_video_id(youtube_url)
        if not video_id:
            return jsonify({"error": "Invalid YouTube URL format"}), 400

        # Create temporary filename for status updates
        temp_filename = f"YouTube Video ({video_id}).yt"

        # Send initial status
        broadcast_upload_status(temp_filename, "processing", "Extracting transcript from YouTube...")

        logger.info(f"Extracting transcript for video ID: {video_id}")

        # Get transcript using RapidAPI (following sample code)
        transcript_result = get_youtube_transcript_rapidapi(video_id)

        if not transcript_result['success']:
            broadcast_upload_status(temp_filename, "error", error="Failed to extract transcript from YouTube")
            return jsonify({"error": "Failed to extract transcript from YouTube"}), 400

        # Extract title and transcript
        video_title = transcript_result.get('title', 'YouTube Video')
        # Add .yt to the end of the title
        if not video_title.endswith('.yt'):
            video_title = video_title + '.yt'
        transcript_text = transcript_result.get('cleanedTranscript', '')

        if not transcript_text:
            broadcast_upload_status(temp_filename, "error", error="No transcript text found")
            return jsonify({"error": "No transcript text found"}), 400

        logger.info(f"Successfully extracted transcript for: {video_title}")
        logger.info(f"Transcript length: {len(transcript_text)} characters")

        # Update status with actual title
        broadcast_upload_status(video_title, "processing", "Uploading transcript to database...")

        # Call the upload-youtube-transcript endpoint
        try:
            upload_url = f"{CHROMADB_BASE_URL}/upload-youtube-transcript"
            upload_payload = {
                "transcript_text": transcript_text,
                "video_title": video_title,
                "video_url": youtube_url,
                "source": "youtube_transcripts"
            }

            logger.info(f"Uploading transcript to: {upload_url}")
            upload_response = requests.post(upload_url, json=upload_payload, timeout=60)

            if upload_response.status_code == 200:
                logger.info("Successfully uploaded transcript to ChromaDB")
                upload_data = upload_response.json()

                # Send success status
                broadcast_upload_status(video_title, "success", "Transcript uploaded successfully")

                return jsonify({
                    "success": True,
                    "video_title": video_title,
                    "video_url": youtube_url,
                    "transcript_length": len(transcript_text),
                    "upload_response": upload_data
                })
            else:
                error_msg = f"Failed to upload transcript: {upload_response.text}"
                logger.error(f"Failed to upload to ChromaDB: {upload_response.status_code} - {upload_response.text}")
                broadcast_upload_status(video_title, "error", error=error_msg)
                return jsonify({"error": error_msg}), 500

        except Exception as upload_error:
            error_msg = f"Upload failed: {str(upload_error)}"
            logger.error(f"Error uploading transcript: {upload_error}")
            broadcast_upload_status(video_title, "error", error=error_msg)
            return jsonify({"error": error_msg}), 500

    except Exception as e:
        logger.error(f"Error in extract_youtube_transcript: {e}")
        if temp_filename:
            broadcast_upload_status(temp_filename, "error", error=str(e))
        return jsonify({"error": str(e)}), 500

def get_youtube_transcript_rapidapi(video_id):
    """Get transcript using RapidAPI YouTube Transcriptor."""
    import http.client
    import json

    try:
        conn = http.client.HTTPSConnection("youtube-transcriptor.p.rapidapi.com")

        headers = {
            'x-rapidapi-key': "**************************************************",
            'x-rapidapi-host': "youtube-transcriptor.p.rapidapi.com"
        }

        endpoint = f"/transcript?video_id={video_id}&lang=en"
        conn.request("GET", endpoint, headers=headers)

        res = conn.getresponse()
        data = res.read()

        if res.status == 200:
            payload = json.loads(data.decode("utf-8"))
            cleaned_transcript, raw_used = extract_transcript_text(payload)

            # Extract metadata
            data_obj = payload[0] if isinstance(payload, list) and payload else payload
            title = data_obj.get('title', 'YouTube Video')
            duration = data_obj.get('duration') or data_obj.get('lengthInSeconds')
            language = data_obj.get('lang') or data_obj.get('language', 'en')

            return {
                "success": bool(cleaned_transcript),
                "cleanedTranscript": cleaned_transcript,
                "title": title,
                "duration": duration,
                "language": language,
                "rawTranscriptUsed": raw_used
            }
        else:
            logger.error(f"RapidAPI error: {res.status} - {data.decode('utf-8')}")
            return {"success": False}

    except Exception as e:
        logger.error(f"Error calling RapidAPI: {e}")
        return {"success": False}

def extract_transcript_text(payload):
    """Extract and clean transcript text from API response."""
    import re

    data = payload
    if isinstance(data, list) and data:
        data = data[0]

    transcript_text = None
    raw_used = None

    # Try different transcript fields
    for key in ("transcriptionAsText", "text"):
        if isinstance(data.get(key), str) and data[key].strip():
            transcript_text = data[key]
            raw_used = data[key]
            break

    # Try arrays under different keys
    if transcript_text is None:
        if isinstance(data.get("transcription"), list) and data["transcription"]:
            parts = []
            for seg in data["transcription"]:
                seg_text = (seg.get("subtitle") or seg.get("text") or "").strip()
                if seg_text:
                    parts.append(seg_text)
            if parts:
                transcript_text = " ".join(parts)
                raw_used = data["transcription"]

    # Try transcript field
    if transcript_text is None and "transcript" in data:
        if isinstance(data["transcript"], str) and data["transcript"].strip():
            transcript_text = data["transcript"]
            raw_used = data["transcript"]
        elif isinstance(data["transcript"], list) and data["transcript"]:
            parts = []
            for seg in data["transcript"]:
                seg_text = (seg.get("text") or seg.get("subtitle") or "").strip()
                if seg_text:
                    parts.append(seg_text)
            if parts:
                transcript_text = " ".join(parts)
                raw_used = data["transcript"]

    # Clean the text
    cleaned = clean_transcript_text(transcript_text or "")
    return cleaned, raw_used

def clean_transcript_text(txt):
    """Clean and normalize transcript text."""
    import re

    if not txt:
        return ""

    # Collapse whitespace and normalize
    cleaned = re.sub(r"\s+", " ", txt)
    cleaned = cleaned.replace("&quot;", '"')
    cleaned = cleaned.replace("&#39;", "'")
    cleaned = cleaned.replace("&amp;", "&")
    cleaned = cleaned.replace("&lt;", "<")
    cleaned = cleaned.replace("&gt;", ">")
    cleaned = cleaned.strip()
    cleaned = cleaned.replace(" .", ".")

    return cleaned

def extract_video_id(youtube_url):
    """Extract video ID from various YouTube URL formats (from sample code)."""
    from urllib.parse import urlparse, parse_qs

    parsed = urlparse(youtube_url)
    host = (parsed.hostname or "").lower()

    if host == "youtu.be":
        return parsed.path.lstrip("/") or None

    if host in ("www.youtube.com", "youtube.com", "m.youtube.com"):
        return parse_qs(parsed.query).get("v", [None])[0]

    # fallback: maybe user already passed a raw id
    return youtube_url if youtube_url and len(youtube_url) >= 8 else None

@app.route('/api/speech-to-text', methods=['POST'])
def speech_to_text():
    """Convert uploaded audio to text using Groq Whisper."""
    try:
        # Check if Groq client is available
        if not groq_client:
            return jsonify({"error": "Groq API not configured"}), 500

        if 'audio' not in request.files:
            return jsonify({"error": "No audio file provided"}), 400

        audio_file = request.files['audio']
        if audio_file.filename == '':
            return jsonify({"error": "No audio file selected"}), 400

        # Transcribe using Groq
        logger.info("Transcribing audio with Groq Whisper...")
        transcript_text = transcribe_with_groq(audio_file)

        if transcript_text:
            logger.info(f"Successfully transcribed audio: {transcript_text[:50]}...")
            return jsonify({"text": transcript_text})
        else:
            return jsonify({"error": "Transcription failed"}), 500

    except Exception as e:
        logger.error(f"Speech-to-text error: {e}")
        return jsonify({"error": "Failed to transcribe audio"}), 500

def transcribe_with_groq(audio_file):
    """Transcribe audio using Groq Whisper API."""
    # Read audio file into memory
    audio_data = audio_file.read()
    audio_file.seek(0)  # Reset file pointer

    # Create file tuple for Groq API
    file_tuple = (audio_file.filename or "audio.wav", BytesIO(audio_data), "audio/wav")

    # Call Groq API
    response = groq_client.audio.transcriptions.create(
        model=GROQ_MODEL,
        file=file_tuple,
        language="en",  # Default to English, can be made configurable
        response_format="text"
    )

    return response

@app.route('/api/models')
def get_models():
    """Get available AI models."""
    try:
        models = []
        for model_key, model_config in AI_MODELS.items():
            models.append({
                'id': model_key,
                'name': model_config['name']
            })

        return jsonify({
            "models": models,
            "default_model": DEFAULT_MODEL
        })
    except Exception as e:
        logger.error(f"Error getting models: {e}")
        return jsonify({"error": "Failed to get models"}), 500

@app.route('/api/send-message', methods=['POST'])
def send_message():
    """Send user message to n8n webhook."""
    try:
        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({"error": "No message provided"}), 400

        message = data['message']
        selected_model = data.get('model', DEFAULT_MODEL)
        # selectedSources removed - all sources are now used by default

        # Validate model selection
        if selected_model not in AI_MODELS:
            logger.error(f"Invalid model selected: {selected_model}")
            return jsonify({"error": f"Invalid model: {selected_model}"}), 400

        # Get webhook URL for selected model
        webhook_url = AI_MODELS[selected_model]['webhook_url']

        if not webhook_url:
            return jsonify({"error": f"Webhook URL not configured for model: {selected_model}"}), 500

        message_id = data.get('messageId', int(time.time()))

        # All sources are now used by default (no filtering)
        logger.info("Query will use all available sources")

        # Use host.docker.internal for Docker containers to reach host machine
        docker_host = "host.docker.internal"

        # Determine protocol based on SSL configuration
        protocol = "https" if globals().get('SSL_ENABLED', False) else "http"

        # Prepare payload for n8n
        payload = {
            "message": message,
            "messageId": message_id,
            "model": selected_model,
            "timestamp": request.headers.get('X-Timestamp', ''),
            "source": "question_page",
            "replyWebhook": f"{protocol}://{docker_host}:{PORT}/api/ai-response"
            # selectedSources removed - all sources are used by default
        }

        # Log the payload being sent
        logger.info(f"Sending to {selected_model} model via n8n: {payload}")

        # Send to n8n webhook
        response = requests.post(
            webhook_url,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )

        if response.status_code == 200:
            logger.info(f"Successfully sent message to {selected_model}: {message[:50]}...")
            logger.info(f"Reply webhook URL: {payload['replyWebhook']}")
            return jsonify({"status": "success", "message": "Message sent successfully", "model": selected_model})
        else:
            logger.error(f"n8n webhook error {response.status_code}: {response.text}")
            return jsonify({"error": f"Failed to send message to {selected_model}"}), 500

    except requests.RequestException as e:
        logger.error(f"Error sending to n8n: {e}")
        return jsonify({"error": "Failed to connect to n8n"}), 500
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/ai-response', methods=['POST'])
def receive_ai_response():
    """Receive AI response from n8n webhook."""
    try:
        # Log the raw request for debugging
        logger.info(f"Received AI response webhook call")
        logger.info(f"Request headers: {dict(request.headers)}")
        logger.info(f"Request data: {request.get_data()}")

        data = request.get_json()
        logger.info(f"Parsed JSON data: {data}")
        logger.info(f"Data type: {type(data)}")

        if not data:
            logger.error("No JSON data provided in AI response")
            return jsonify({"error": "No data provided"}), 400

        # Handle the n8n response format: [{"output": "response text"}]
        ai_message = ""
        message_id = ""

        if isinstance(data, list) and len(data) > 0:
            # n8n format: [{"output": "response text"}]
            first_item = data[0]
            logger.info(f"First item in array: {first_item}")
            logger.info(f"First item type: {type(first_item)}")
            logger.info(f"First item keys: {list(first_item.keys()) if isinstance(first_item, dict) else 'Not a dict'}")

            ai_message = first_item.get('output', '')
            message_id = first_item.get('messageId', '')
            logger.info(f"Detected n8n array format")
        elif isinstance(data, dict):
            # Direct format: {"message": "response text", "messageId": "123"}
            logger.info(f"Dict keys: {list(data.keys())}")
            ai_message = data.get('message', '') or data.get('output', '')
            message_id = data.get('messageId', '')
            logger.info(f"Detected direct object format")
        else:
            logger.error(f"Unexpected data format: {type(data)}")

        logger.info(f"Extracted message: '{ai_message}', messageId: '{message_id}'")

        if not ai_message:
            logger.error("No message/output field in AI response data")
            return jsonify({"error": "No message or output provided"}), 400

        # Filter out empty or whitespace-only messages
        if not ai_message.strip():
            logger.info("Received empty message, ignoring")
            return jsonify({"status": "ignored", "message": "Empty message ignored"}), 200

        # Broadcast the response to all connected clients
        response_data = {
            "message": ai_message.strip(),
            "messageId": message_id,
            "timestamp": time.time(),
            "type": "ai_response"
        }

        with response_lock:
            # Send to all connected SSE clients
            for client_id, client_queue in ai_response_queues.items():
                try:
                    client_queue.put_nowait(response_data)
                    logger.info(f"Sent response to client {client_id}")
                except queue.Full:
                    logger.warning(f"Queue full for client {client_id}")

            logger.info(f"Broadcasted AI response: {response_data}")
            logger.info(f"Active SSE clients: {len(ai_response_queues)}")

        logger.info(f"Successfully received AI response for message {message_id}: {ai_message[:50]}...")
        return jsonify({"status": "success", "message": "Response received"})

    except Exception as e:
        logger.error(f"Error receiving AI response: {e}")
        return jsonify({"error": "Internal server error"}), 500

# In-memory upload status map (optional cache for last-known states)
upload_status_map = {}

@app.route('/api/upload-status', methods=['POST'])
def upload_status_notification():
    """Receive upload status notifications from the Chroma server and broadcast via SSE."""
    try:
        data = request.get_json() or {}

        # Normalize payload
        payload = {
            "type": "upload_status",
            "endpoint_type": data.get("endpoint_type"),
            "status": data.get("status"),
            "filename": data.get("filename") or data.get("source"),
            "session_id": data.get("session_id"),
            "processed_chunks": data.get("processed_chunks"),
            "total_chunks": data.get("total_chunks"),
            "message": data.get("message"),
            "error": data.get("error") or data.get("error_details"),
            "timestamp": time.time()
        }

        # Store last-known status by session or filename
        key = payload.get("session_id") or payload.get("filename")
        if key:
            upload_status_map[key] = payload

        # Broadcast to all connected SSE clients
        with response_lock:
            for client_id, client_queue in ai_response_queues.items():
                try:
                    client_queue.put_nowait(payload)
                except queue.Full:
                    logger.warning(f"Upload status queue full for client {client_id}")

        return jsonify({"status": "success", "message": "Status notification received"})

    except Exception as e:
        logger.error(f"Error in upload_status_notification: {e}")
        return jsonify({"error": "Internal server error"}), 500

        logger.error(f"Error receiving AI response: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/stream-responses')
def stream_responses():
    """Server-Sent Events endpoint for real-time AI responses."""
    def event_stream():
        # Create a unique client ID and queue
        client_id = f"client_{int(time.time())}_{threading.current_thread().ident}"
        client_queue = queue.Queue(maxsize=50)

        with response_lock:
            ai_response_queues[client_id] = client_queue
            logger.info(f"New SSE client connected: {client_id}")

        try:
            while True:
                try:
                    # Wait for a response with timeout
                    response_data = client_queue.get(timeout=30)
                    yield f"data: {json.dumps(response_data)}\n\n"
                except queue.Empty:
                    # Send heartbeat to keep connection alive
                    yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"
                except Exception as e:
                    logger.error(f"Error in SSE stream for {client_id}: {e}")
                    break
        finally:
            # Clean up when client disconnects
            with response_lock:
                if client_id in ai_response_queues:
                    del ai_response_queues[client_id]
                    logger.info(f"SSE client disconnected: {client_id}")

    return Response(event_stream(), mimetype='text/event-stream',
                   headers={'Cache-Control': 'no-cache',
                           'Connection': 'keep-alive'})

@app.route('/api/test-response', methods=['POST'])
def test_ai_response():
    """Test endpoint to simulate an AI response."""
    try:
        data = request.get_json() or {}
        test_message = data.get('message', 'This is a test AI response!')
        message_id = data.get('messageId', 'test-123')

        # Broadcast the test response to all connected clients
        response_data = {
            "message": test_message,
            "messageId": message_id,
            "timestamp": time.time()
        }

        with response_lock:
            # Send to all connected SSE clients
            for client_id, client_queue in ai_response_queues.items():
                try:
                    client_queue.put_nowait(response_data)
                except queue.Full:
                    logger.warning(f"Queue full for client {client_id}")

        logger.info(f"Added test AI response: {test_message}")
        return jsonify({"status": "success", "message": "Test response added"})

    except Exception as e:
        logger.error(f"Error in test response: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/telegram-reply', methods=['POST'])
def telegram_reply():
    """Endpoint for n8n to send AI replies back to Telegram users."""
    try:
        if not TELEGRAM_BOT_AVAILABLE:
            return jsonify({"error": "Telegram bot not available"}), 503

        telegram_processor = get_telegram_processor()

        data = request.get_json()
        logger.info(f"Received Telegram reply request: {data}")

        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Extract reply message and Telegram chat info
        reply_message = data.get('message') or data.get('reply') or data.get('output')
        chat_id = data.get('telegram_chat_id') or data.get('chat_id')
        reply_to_message_id = data.get('telegram_message_id') or data.get('message_id')

        if not reply_message:
            return jsonify({"error": "No reply message provided"}), 400

        if not chat_id:
            return jsonify({"error": "No chat_id provided"}), 400

        logger.info(f"Sending reply to Telegram chat {chat_id}: {reply_message[:50]}...")

        # Send reply to Telegram using direct HTTP request (no async/event loop issues)
        success = telegram_processor.send_reply_sync(
            chat_id=int(chat_id),
            message=reply_message,
            reply_to_message_id=int(reply_to_message_id) if reply_to_message_id else None
        )

        if success:
            logger.info(f"Successfully sent reply to Telegram chat {chat_id}")
            return jsonify({"status": "success", "message": "Reply sent to Telegram"})
        else:
            logger.error(f"Failed to send reply to Telegram chat {chat_id}")
            return jsonify({"error": "Failed to send reply to Telegram"}), 500

    except ValueError as e:
        logger.error(f"Invalid chat_id or message_id: {e}")
        return jsonify({"error": "Invalid chat_id or message_id"}), 400
    except Exception as e:
        logger.error(f"Error sending Telegram reply: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/health')
def health_check():
    """Health check endpoint."""
    docker_host = "host.docker.internal"

    # Determine protocol based on SSL configuration
    protocol = "https" if globals().get('SSL_ENABLED', False) else "http"

    # Check model configurations
    model_status = {}
    for model_key, model_config in AI_MODELS.items():
        model_status[model_key] = {
            "name": model_config['name'],
            "webhook_configured": bool(model_config['webhook_url'])
        }

    return jsonify({
        "status": "ok",
        "message": "Question Page is running",
        "chromadb_configured": bool(CHROMADB_BASE_URL),
        "n8n_configured": bool(N8N_WEBHOOK_URL),
        "groq_configured": bool(GROQ_API_KEY),
        "speech_to_text_available": bool(groq_client),
        "telegram_bot_available": TELEGRAM_BOT_AVAILABLE,
        "telegram_bot_configured": bool(globals().get('TELEGRAM_BOT_TOKEN', 'your-telegram-bot-token-here') != 'your-telegram-bot-token-here'),
        "telegram_webhook_configured": bool(globals().get('TELEGRAM_WEBHOOK_URL', '')),
        "telegram_bot_running": get_telegram_processor().is_running if TELEGRAM_BOT_AVAILABLE else False,
        "ai_response_webhook": f"{protocol}://{docker_host}:{PORT}/api/ai-response",
        "telegram_reply_webhook": f"{protocol}://{docker_host}:{PORT}/api/telegram-reply",
        "active_sse_clients": len(ai_response_queues),
        "current_host": request.host,
        "docker_webhook_url": f"{protocol}://{docker_host}:{PORT}/api/ai-response",
        "available_models": model_status,
        "default_model": DEFAULT_MODEL
    })

if __name__ == '__main__':
    # Check configuration
    print("Starting Question Page Flask app...")
    print(f"ChromaDB URL: {CHROMADB_BASE_URL}")
    print(f"n8n Webhook URL: {N8N_WEBHOOK_URL}")

    # Check speech-to-text configuration
    if groq_client:
        print(f"✓ Groq API configured (Model: {GROQ_MODEL})")
    else:
        print("WARNING: No speech-to-text API configured.")
        print("Please set GROQ_API_KEY in config_local.py or environment variable.")

    if not N8N_WEBHOOK_URL:
        print("WARNING: N8N_WEBHOOK_URL not set. Message sending will not work.")
        print("Please set your n8n webhook URL in config_local.py or environment variable.")

    # HTTPS info and certificate management
    try:
        if SSL_ENABLED:
            print("HTTPS is enabled - checking SSL certificate...")

            # Ensure SSL certificate is valid and regenerate if needed
            if ensure_ssl_certificate():
                print(f"✓ SSL certificate is valid")
                print(f"Cert: {SSL_CERT_PATH}")
                print(f"Key:  {SSL_KEY_PATH}")
                print(f"Access the app at: https://localhost:{PORT}")
                print(f"Access the app at: https://************:{PORT}")
            else:
                print("WARNING: SSL certificate validation failed. Falling back to HTTP.")
                SSL_ENABLED = False
                print(f"Access the app at: http://localhost:{PORT}")
        else:
            print(f"Access the app at: http://localhost:{PORT}")
    except NameError:
        print(f"Access the app at: http://localhost:{PORT}")

    # Start Telegram bot if available and configured
    if TELEGRAM_BOT_AVAILABLE:
        # Check if Telegram bot token is configured
        telegram_token = globals().get('TELEGRAM_BOT_TOKEN', 'your-telegram-bot-token-here')
        if telegram_token and telegram_token != 'your-telegram-bot-token-here':
            print("Starting Telegram bot...")
            start_telegram_bot()
            print(f"✓ Telegram bot started and polling for messages")
            print(f"Telegram reply endpoint: http{'s' if 'SSL_ENABLED' in globals() and SSL_ENABLED else ''}://localhost:{PORT}/api/telegram-reply")
        else:
            print("WARNING: Telegram bot token not configured.")
            print("Please set TELEGRAM_BOT_TOKEN in config_local.py or environment variable.")
    else:
        print("WARNING: Telegram bot not available.")

    # Configure SSL context if enabled
    ssl_context = None
    try:
        if SSL_ENABLED:
            cert_path = SSL_CERT_PATH
            key_path = SSL_KEY_PATH

            # Double-check certificate files exist after potential regeneration
            if os.path.exists(cert_path) and os.path.exists(key_path):
                # Use simple tuple format for Flask SSL context (most compatible)
                ssl_context = (cert_path, key_path)
                print(f"✓ SSL context configured with {cert_path} and {key_path}")
                print("✓ HTTPS enabled - Flask will handle SSL/TLS protocol")
            else:
                print("WARNING: SSL enabled but certificate files not found after regeneration attempt.")
                print("Falling back to HTTP...")
                SSL_ENABLED = False
    except NameError:
        pass
    except Exception as e:
        print(f"WARNING: SSL configuration failed: {e}")
        print("Falling back to HTTP...")
        ssl_context = None

    # Start Flask app
    if ssl_context:
        print(f"🔒 Starting HTTPS server on port {PORT}")
        print(f"📱 Access at: https://************:{PORT}")
        print(f"🏠 Local access: https://localhost:{PORT}")
    else:
        print(f"🌐 Starting HTTP server on port {PORT}")
        print(f"📱 Access at: http://************:{PORT}")
        print(f"🏠 Local access: http://localhost:{PORT}")

    app.run(debug=DEBUG, host=HOST, port=PORT, ssl_context=ssl_context)
