YouTube Video: OpenAI Unveils GPT-5: Everything Announced at OpenAI's Summer Update in 12 Minutes
URL: https://www.youtube.com/watch?v=4H9pIAXkELw
Video ID: 4H9pIAXkELw
Duration: 714
Language: en
Extracted on: 2025-08-08 14:55:39
--------------------------------------------------

Good morning. 32 months ago, we launched Chat GBT. And since then, it has become the default way that people use AI. In that first week, a million people tried it out. And we thought that was pretty incredible. But now about 700 million people use Chat GPT every week and increasingly rely on it to work, to learn, for advice, to create, and much more. Today, finally, we're launching GPT5. GPT5 is a major upgrade over GPT4 and a significant step along our path to AGI. Now, today we're going to show you some incredible demos. We'll talk about some performance metrics, but the important point is this. We think you will love using GPT5 much more than any previous AI. It is useful. It is smart. It is fast. And it's intuitive. But it's not only asking. Now, GPT5 can also do stuff for you. It can write an entire computer program from scratch to help you with whatever you'd like. And we think this idea of software on demand is going to be one of the defining characteristics of the GPT5 era. Until now, our users have had to pick between the fast responses of standard GPTs or the slow, more thoughtful responses from our reasoning models. But GPD5, it eliminates this choice. It aims to think just the perfect amount to give you the perfect answer. Today we're going to show a series of demos in coding, in writing, in learning, and in health. But GPD5 isn't limited to these domains. It's very useful in all cases where you require deep reasoning or expert level knowledge in things like math, in physics, even in things like law. And the exciting thing is we're excited to make this available to everyone, even to our free tier. The best part is that we're bringing this frontier intelligence to all users. GBT5 is rolling out today for free plus pro and team users and next week we'll roll it out to enterprise and edu. For the first time, our most advanced model will be available to the free tier. Free users will start with GPT5 and when they hit their limit, they'll transition to GBT5 Mini, a smaller but still highly capable model. It actually outperforms 03 on many dimensions. Plus, users will still have significantly higher usage than free users. And our pro subscribers will get unlimited GPD5 along with GPT5 Pro extended thinking for even more detailed and reliable response when you just need that extra depth. Suppose your kid is in middle school physics and they want to learn about Bernoli effect. They need your help with their homework and you might be like, "Wait, I might need some help with that too." So, you could ask Gypt 5, give me a quick refresher on the Bernoli effect and why airplanes are the shape they are. Since this is a pretty straightforward prompt, um, actually doesn't need extra time to think about it and answers right away, but it still gives me a high quality answer and explains the concept clearly. So here it says like Bernoli fan means like faster moving fluid has lower pressure and slowing moving fluid has higher pressure. So to make this even more helpful I'm going to ask GP5 to create a moving demo to illustrate this. So I could ask explain this in detail and create a moving SVG in the canvas tool to show me. This is a pretty complex task because now GT5 actually needs to build the visual. Therefore GT5 takes a moment to think through the answer so you can come back with something more comprehensive and accurate. So you see that Gift5 has already written like 200 more than 200 lines of code already. Um and while the model is thinking you can also tap here to expand the train of thought to actually see what's going on under the hood. For example the GPT5 was thinking about oh the user wants a moving SVG visualization in canvas. I actually need to create HTML code to do that. Okay so it's already done. So look like CH GPT just finished like 300 or we're near 400 lines of code in two minutes. So let's see if the code can actually run. Okay. Oh wow. Nice. Yeah. So with just a simple prompt, GT5 created this interactive and engaging demo that I can actually play with. So I can actually change the air speed here to see how the lift and the pressure change accordingly. I can also tweak the angle of attack to see if my plane will actually fly or crash. I hope not. Yeah. I will actually try to build something that I would find useful uh which is building a web app for my partner to learn how to speak French so that she can better communicate with my family. So here I have a prompt. I will execute it. It asks exactly what I just said. Um, please build a web app for my partner to learn French. One thing to note is that GPD5, just like many of our other models, have a lot has a lot of diversity in it answers. So what I like doing, especially when you do uh this type of vibe coding, is to take this message and ask it multiple times to GPT5 and then you can decide which one you prefer. So, I'm going to open a few tabs. Just going to paste there. Great. So, while it's working on it, let's read through exactly the prompt I wrote. Create a beautiful and highly interactive web app for my partner, an English speaker, uh, to learn French. And then I gave a little bit more details. Um, track her daily progress. Use a highly engaging theme. Oh, it's already working. I'm going to put it on the side for now. Use a highly engaging theme. include a variety of activities like flashcards and quizzes that she can interact with. And then to make it even more fun for her, uh I actually asked GPT5 to embed an educational game uh which is based on the old snake game, but I asked to add a French touch to it which is to uh replace this the snake with a mouse and the apples with cheese. So I can simply press run code. So, I'll do that and cross my fingers. Whoa. Oh, nice. Voila. So, we have a a nice u a nice website. Uh name is Midnight in Paris. Oh, I love together. Super romantic. Um we also see a few tabs, flashcards, quiz, and mouse and cheese. Exactly like I asked for. Uh I will play that. So, this says Luca, which says the cat. Sorry. Luca. Well, that's pretty good pronunciation. What does that mean? The cat. Oh, so I can reveal and check if GB5 is correct. It is. Um, so if I press next, oh, and I don't know if you saw, I think it actually updated the progress bar, which is exactly what I had asked for. Let's check the quiz. Here's the word no, which is no. So, if I press on which, which means congrats. And it updated. It updated the progress bar again. H. And let's check the mouse and cheese tab. Okay, that seems like a mouse. Here's the cheese. Um, I'm going to try to play it. Uh, I'm can't promise I'm going to be good at it. Okay, seems to be working. Indeed, just when I eat the cheese, it gives me a new French word. It's actually super complicated and I already lost. So, we've been steadily improving voice over the past year to make it more useful for everyone. First, it sounds incredibly natural, just like you're talking to a real person. Second, we've added the video so that it sees what you see while chatting with you. Third, it also translate between languages consistently and smoothly across turns. But today, we're doing something very special where we are bringing our best voice experience to everyone. Free users can now chat for hours while paid subscribers can have nearly unlimited access and voice is also available in custom GPT. Plus, subscribers now can custom tailor the voice experience exactly to their need. It will follow your instruction closely. Let's try something fun. Excited. So, I can ask the voice model to give me a comprehensive answer, a concise one, or even just a single word. Hey Chad, could you only answer to me in one word please from now? Absolutely. Okay. Describe the plot of the book Pride and Prejudice. Relationships. Okay, that's true. Yeah. Yeah. Give me a piece of wisdom. Patience. Patience. So that's the word of wisdom from our voice model. And I think our model is trying to say, "Thank you for your patient waiting for GBD5." Yeah. So, um, I have a younger cousin and I want to make a game for her. So, I I want to make a 3D game that incorporates a castle. So, you can see my prompt. Um, I'll just kick this off. Uh, sorry. It's always the non AR parts. Yeah, exactly. Uh, yeah. Okay. So, you can see my prompt. um create a beautiful castle. I've included some details like we want people patrolling the walls, some movement, horses. Um and I want a miniame where I can pop balloons by clicking on them. And this should make a sound effect. So let me run this in cursor. Um I'll just paste it in. And um I'm I'm going to show um an example that I've already generated just to save some time. Um, so here is the beautiful castle that the model made. So it's just wild how, you know, from a concise prompt, the model has this great sense of aesthetics where it's it's made this like floating rock um made a 3D castle and if you zoom in, you can see like tons of detail like these guards that are walking around, cannons firing. Do you want to fire the cannons if you click this button? Yes, of course. Who wouldn't want to? Yeah, there we go. So can fire the cannons. Um you can even chat with the characters. So we'll say hi to Captain Rowan. They have names. Names. Say hello to the merchant. Merchants selling some stuff. Uh what's your favorite song? A ballad of banners and dons. Nice. Give me some wisdom. Curiosity is volatile. Yeah, that's that makes sense. Um, mini game. Yeah. Do you guys want to try the mini game? Absolutely. Let's play the mini game. So, if you hit this if you hit this button, you want to try it, Greg? All right. So, you can fire at these balloons. Oh, wow. All right. Oh, no. I'm not good at it. Hold on. Maybe I can ask GPD 5 for some help with it. Oh, you you hit one. Yes, I got one. Oh, there we go. We got a sound effect. Sound effect. These are historically accurate balloons. Yes. Working with GPD5 has been really fun and profound for me because for me, this is the first model I've worked with that actually has a sense of creativity. And we're really excited to see how GP5 unlocks your creativity.