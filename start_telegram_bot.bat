@echo off
REM Telegram Bot Auto-Start Script
REM This script starts the Flask app with Telegram bot in virtual environment
REM Console window will be hidden for clean startup

REM Change to the project directory
cd /d "C:\code\AI_Agent_Project_Improved\Question_Page"

REM Start the Flask app using virtual environment Python
REM The /B flag runs the command in the background without opening a new window
start /B /MIN "" "C:\code\AI_Agent_Project_Improved\Question_Page\venv\Scripts\python.exe" "C:\code\AI_Agent_Project_Improved\Question_Page\page.py"

REM Exit the batch file immediately (don't wait for the Python process)
exit
