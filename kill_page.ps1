# Kill python process running Question_Page\page.py
$procs = Get-CimInstance Win32_Process -Filter "Name='python.exe'" |
    Where-Object { $_.CommandLine -match 'Question_Page\\page\.py' }
if ($procs) {
    foreach ($p in $procs) {
        Write-Host ("Killing PID {0}" -f $p.ProcessId)
        Stop-Process -Id $p.ProcessId -Force
    }
    Write-Host "Stopped."
} else {
    Write-Host "No matching process found."
}

