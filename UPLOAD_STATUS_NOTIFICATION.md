# Upload Status Notification System

## Overview

This document describes the new upload status notification system that has been added to the ChromaDB server. This system allows external applications to receive real-time status updates when PDF or YouTube transcript uploads are processed.

## How It Works

When the following endpoints are called:
- `POST /upload-pdf` (http://localhost:5555/upload-pdf)
- `POST /upload-youtube-transcript` (http://localhost:5555/upload-youtube-transcript)

These endpoints will automatically send status notifications to a new endpoint:
- `POST /api/upload-status` (http://localhost:5555/api/upload-status)

## Status Notification Endpoint

### Endpoint Details
- **URL**: `http://localhost:5555/api/upload-status`
- **Method**: `POST`
- **Content-Type**: `application/json`

### Request Format
```json
{
    "endpoint_type": "pdf|youtube",
    "status": "started|processing|completed|error",
    "filename": "document.pdf",
    "session_id": "session-uuid",
    "timestamp": "2025-01-08T12:00:00Z",
    "message": "Status description",
    "error_details": "Error details (optional, only for error status)"
}
```

### Response Format
```json
{
    "status": "success",
    "message": "Status notification received",
    "received_at": "2025-01-08T12:00:00Z",
    "notification_data": {
        "endpoint_type": "pdf",
        "status": "completed",
        "filename": "document.pdf",
        "session_id": "session-uuid",
        "timestamp": "2025-01-08T12:00:00Z"
    }
}
```

## Status Types

### PDF Upload Statuses
1. **started** - PDF upload has begun
2. **processing** - PDF content extraction and processing started
3. **completed** - PDF processed successfully
4. **error** - An error occurred during processing

### YouTube Transcript Upload Statuses
1. **started** - YouTube transcript upload has begun
2. **processing** - Transcript processing started
3. **completed** - Transcript processed successfully
4. **error** - An error occurred during processing

## Example Notification Flow

### PDF Upload Example
```
1. POST /upload-pdf → Status: "started"
2. Processing begins → Status: "processing"
3. Processing completes → Status: "completed" (or "error" if failed)
```

### YouTube Transcript Upload Example
```
1. POST /upload-youtube-transcript → Status: "started"
2. Processing begins → Status: "processing"
3. Processing completes → Status: "completed" (or "error" if failed)
```

## Implementation Details

### Files Modified
- `src/routes/api_routes.py` - Added the new status notification endpoint
- `src/routes/pdf_routes.py` - Modified upload endpoints to send notifications
- `src/core/utils.py` - Added utility function for sending notifications
- `main.py` - Registered the new endpoint route
- `API_ENDPOINTS_AND_WORKFLOW.md` - Updated documentation

### Key Functions
- `upload_status_notification()` - Handles incoming status notifications
- `send_upload_status_notification()` - Sends status notifications to the endpoint

## Testing

A test script has been provided: `test_status_endpoint.py`

To test the endpoint:
```bash
python test_status_endpoint.py
```

This will send sample notifications to verify the endpoint is working correctly.

## Use Cases

This notification system enables:

1. **Real-time Monitoring** - External applications can track upload progress
2. **Error Handling** - Immediate notification of processing failures
3. **Workflow Integration** - Trigger downstream processes based on upload status
4. **User Interface Updates** - Update UI components with real-time status
5. **Logging and Analytics** - Centralized tracking of upload activities

## Configuration

The notification system uses a short timeout (5 seconds) to avoid blocking the main upload process. If the notification endpoint is unavailable, the upload process will continue normally, and a warning will be logged.

## Error Handling

- If the status notification fails to send, the upload process continues normally
- Errors are logged but do not affect the main upload functionality
- The system is designed to be non-blocking and fault-tolerant

## Frontend Integration

### Session ID Support
Both upload endpoints now include `session_id` in their responses:

**PDF Upload Response:**
```json
{
    "status": "success",
    "session_id": "uuid-session-id",
    "text": "Extracted content...",
    "workflow_triggered": true,
    "text_stats": { ... }
}
```

**YouTube Transcript Response:**
```json
{
    "status": "success",
    "session_id": "uuid-session-id",
    "data": { ... }
}
```

### Optional UI Enhancements

The frontend can implement these optional improvements:

1. **Auto-clear Status Indicators**
   - Timeout/auto-clear status indicators after N seconds
   - Prevents stale status messages from remaining visible

2. **Progress Tracking**
   - Show `processed_chunks/total_chunks` progress
   - Real-time updates as chunks are processed
   - Visual progress bars or percentage indicators

3. **Session-based Status Tracking**
   - Use `session_id` to track specific upload progress
   - Match status notifications to specific uploads
   - Handle multiple concurrent uploads

## Future Enhancements

Potential improvements could include:
- Configurable notification endpoints
- Retry mechanisms for failed notifications
- Batch notification support
- Webhook authentication
- Custom notification filters
- Real-time WebSocket connections for instant updates
