# Question Page Development Guide

## Project Overview
Create a question and answer interface that allows users to interact with an AI system using both text and speech input. The interface should integrate with the existing ChromaDB system to provide source-filtered queries.

## Requirements

### 1. Core Functionality
- **Text Input**: Standard text input field for typing questions
- **Speech Input**: Push-to-talk button for voice recording using OpenAI Whisper
- **Source Filtering**: Auto-complete functionality for @filename.pdf syntax
- **Integration**: Connect with existing ChromaDB backend at `http://localhost:5000`

### 2. Input Methods

#### Text Input
- Standard HTML input field with real-time typing support
- Support for @filename.pdf prefix syntax
- Auto-complete dropdown when user types "@"
- Filter suggestions as user continues typing (e.g., "@g" shows files starting with 'g')

#### Speech Input
- Push-to-talk button (press to start, release to stop recording)
- Visual feedback during recording (button state change, recording indicator)
- Convert speech to text using OpenAI Whisper API
- Insert transcribed text into the text input field
- Handle errors gracefully (no microphone, API failures, etc.)

### 3. Source File Auto-completion

#### Data Source
- Retrieve available filenames from ChromaDB metadata field "Source"
- Use existing `/api/documents` endpoint or create new `/api/sources` endpoint
- Cache source list for performance

#### UI Behavior
- Show dropdown when user types "@"
- Filter results in real-time as user types
- Click to select from dropdown
- Keyboard navigation (arrow keys, enter to select, escape to close)
- Show filename without extension in dropdown, but insert full filename

#### Filtering Logic
- Case-insensitive matching
- Match from beginning of filename
- Support partial matching (e.g., "@comp" matches "COMPANY PROFILE.pdf")

### 4. Visual Design

#### Color Scheme (Google Material Design)
```css
--google-blue: #4285f4;
--google-red: #ea4335;
--google-yellow: #fb9804;
--google-green: #34a853;
--google-white: #ffffff;
--google-gray-50: #f8f9fa;
--google-gray-100: #f1f3f4;
--google-gray-200: #e8eaed;
--google-gray-300: #dadce0;
--google-gray-400: #bdc1c6;
--google-gray-500: #9aa0a6;
--google-gray-600: #80868b;
--google-gray-700: #5f6368;
--google-gray-800: #3c4043;
--google-gray-900: #2d2e33;
```

#### Layout
- Clean, modern interface
- Prominent text input field
- Speech button positioned near text input
- Auto-complete dropdown styled consistently
- Responsive design for different screen sizes

### 5. Technical Implementation

#### Frontend
- HTML5 with modern CSS (Flexbox/Grid)
- Vanilla JavaScript or lightweight framework
- Web Audio API for microphone access
- Fetch API for backend communication

#### Backend Integration
- Existing Flask app at `http://localhost:5000`
- ChromaDB collection: "my_pdf_collection"
- Metadata format: `{"Source": "COMPANY PROFILE.pdf"}`

#### APIs Required
1. **Get Sources**: Retrieve unique source filenames
   ```
   GET /api/sources
   Response: {"sources": ["file1.pdf", "file2.pdf", ...]}
   ```

2. **Speech to Text**: Convert audio to text
   ```
   POST /api/speech-to-text
   Body: FormData with audio file
   Response: {"text": "transcribed text"}
   ```

3. **Search**: Submit query (existing endpoint)
   ```
   POST /api/search
   Body: {"query": "@filename.pdf user question", "n_results": 10}
   ```

### 6. File Structure
```
Question_Page/
├── DEVGUIDE.md          # This file
├── page.py              # Flask routes and backend logic
├── templates/
│   └── index.html       # Main page template
├── static/
│   ├── css/
│   │   └── style.css    # Styling
│   └── js/
│       └── app.js       # Frontend JavaScript
└── requirements.txt     # Python dependencies
```

### 7. Dependencies

#### Python (Backend)
- Flask (existing)
- openai (for Whisper API)
- requests (existing)
- chromadb (existing)

#### JavaScript (Frontend)
- No external libraries required (vanilla JS)
- Web Audio API (built-in)
- Fetch API (built-in)

### 8. Implementation Steps

1. **Setup Backend Routes**
   - Create Flask blueprint for Question Page
   - Add `/api/sources` endpoint
   - Add `/api/speech-to-text` endpoint
   - Integrate with existing ChromaDB connection

2. **Create Frontend Structure**
   - HTML template with input fields
   - CSS styling with Google color scheme
   - Basic JavaScript for form handling

3. **Implement Text Input with Auto-complete**
   - Dropdown component
   - Real-time filtering
   - Keyboard navigation

4. **Add Speech Input**
   - Microphone access
   - Audio recording
   - Whisper API integration
   - Error handling

5. **Testing and Refinement**
   - Test with various file types
   - Verify auto-complete accuracy
   - Test speech recognition quality
   - Cross-browser compatibility

### 9. Security Considerations
- Validate file extensions in auto-complete
- Sanitize user input
- Rate limiting for speech API calls
- Secure API key storage for OpenAI Whisper

### 10. Performance Optimization
- Cache source file list
- Debounce auto-complete requests
- Compress audio before sending to Whisper
- Lazy load large file lists

## Next Steps
1. Answer clarifying questions about framework choice and integration approach
2. Set up basic Flask routes and HTML structure
3. Implement core functionality step by step
4. Test and iterate based on user feedback