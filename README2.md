# Whisper Transcription App

A real-time audio transcription application using OpenAI Whisper and Groq APIs with advanced features for voice-to-text conversion, grammar correction, and task delegation.

## Features

- **Real-time Audio Recording**: Record audio directly from your microphone
- **Multiple AI Providers**: Support for both OpenAI Whisper and Groq Whisper Large v3
- **Language Support**: English and Chinese (Simplified) transcription
- **Grammar Correction**: AI-powered text refinement using Llama-3.3-70b-versatile
- **Task Delegation**: Convert transcribed text into professional task formats
- **Meeting Mode**: Extended recording time for long meetings (up to 6 hours)
- **System Tray Integration**: Minimize to system tray for background operation
- **Hotkey Support**: Global hotkeys for quick access
- **Auto-paste**: Automatically paste transcribed text to active applications

## Requirements

- Python 3.8+
- Windows OS (for system tray and hotkey features)
- Microphone access
- Internet connection for API calls

## Installation

1. Clone or download this repository
2. Install required dependencies:
```bash
pip install -r requirements.txt
```

3. Set up API keys (choose one or both):
   - **OpenAI API Key**: Set `OPENAI_API_KEY` environment variable
   - **Groq API Key**: Set `GROQ_API_KEY` environment variable

## API Setup

### Groq API (Recommended - Default Provider)
1. Sign up at [Groq Console](https://console.groq.com/)
2. Generate an API key
3. Set environment variable: `GROQ_API_KEY=your_groq_api_key_here`

### OpenAI API (Alternative Provider)
1. Sign up at [OpenAI Platform](https://platform.openai.com/)
2. Generate an API key
3. Set environment variable: `OPENAI_API_KEY=your_openai_api_key_here`

## Usage

### Starting the Application
```bash
python whisper1directaudiov3.2025.py
```

### Recording Audio
- Click "Record" button or press hotkeys:
  - `Esc` key
  - `F13` key
  - `Alt + ` ` (Alt + backtick)
- Click "Stop" or press the same hotkeys to stop recording

### Transcription Options
1. **Whisper**: Basic transcription only
2. **Whisper+Refine**: Transcription + AI grammar correction (English only)

### Language Selection
- **EN**: English transcription
- **中文**: Chinese transcription (converts to Simplified Chinese)

### Recording Modes
- **Normal Mode**: Up to 10 minutes recording
- **Meeting Mode**: Up to 6 hours recording (check the Meeting Mode checkbox)

### Additional Features
- **Correct (F14)**: Apply grammar correction to current text
- **Correct (Alt+1)**: Alternative grammar correction workflow
- **Taskify (Alt+2)**: Convert text to professional task delegation format
- **Resend Last**: Re-transcribe the last recorded audio file

## GROQ Audio Transcription Method

The application uses GROQ's Whisper Large v3 model for audio transcription. Here's how it works:

### 1. Client Initialization
```python
groq_client = Groq(api_key=groq_api_key)
```

### 2. Audio File Preparation
- Audio is recorded at 16kHz sample rate
- Saved as WAV format
- Normalized and noise-gated for optimal quality

### 3. API Call Structure
```python
response = groq_client.audio.transcriptions.create(
    model="whisper-large-v3",           # GROQ's Whisper v3 model
    file=file_tuple,                    # (filename, BytesIO, mime_type)
    language=language,                  # "en" or "zh"
    response_format="text"              # Plain text output
)
```

### 4. Key Parameters
- **Model**: `whisper-large-v3` (Latest Whisper model)
- **File Format**: Supports WAV, OGG, MP3, and other audio formats
- **Language Codes**:
  - `"en"` for English
  - `"zh"` for Chinese (auto-converts to Simplified)
- **Response Format**: `"text"` for clean text output

### 5. Error Handling
- Automatic retry logic (up to 2 attempts)
- Exponential backoff for rate limiting
- Authentication error detection
- Graceful fallback between providers

### 6. Audio Processing Pipeline
1. **Recording** → Real-time audio capture
2. **Processing** → Noise reduction and normalization
3. **File Creation** → WAV file generation
4. **API Call** → GROQ Whisper transcription
5. **Post-processing** → Language conversion and text cleanup
6. **Output** → Transcribed text with auto-paste

## Configuration

### Audio Settings
- **Sample Rate**: 16kHz (optimal for Whisper)
- **Channels**: Mono
- **Format**: 16-bit PCM
- **Chunk Size**: 4096 samples

### Performance Settings
- **API Timeout**: 45 seconds
- **Processing Timeout**: 20 seconds
- **Thread Pool**: Auto-scaled based on CPU cores
- **Audio Queue**: 500 frame buffer

### File Management
- **Temporary Files**: Stored in system temp directory
- **Auto-cleanup**: Previous audio files automatically deleted
- **Audio Log**: JSON log of processed files

## Hotkeys

| Hotkey | Function |
|--------|----------|
| `Esc` | Start/Stop recording |
| `F13` | Start/Stop recording |
| `Alt + ` ` | Start/Stop recording |
| `F14` | Grammar correction |
| `Alt + 1` | Alternative grammar correction |
| `Alt + 2` | Task delegation format |

## System Tray

The application can minimize to the system tray for background operation:
- Right-click tray icon for context menu
- "Show" to restore window
- "Exit" to close application

## Troubleshooting

### Common Issues

1. **No API Key Error**
   - Ensure environment variables are set correctly
   - Check API key format (Groq: starts with `gsk_`, OpenAI: starts with `sk-`)

2. **Audio Recording Issues**
   - Check microphone permissions
   - Verify audio device availability
   - Try running as administrator

3. **Transcription Errors**
   - Check internet connection
   - Verify API key validity
   - Check API quota/billing status

4. **Performance Issues**
   - Close unnecessary applications
   - Check available RAM
   - Reduce recording quality if needed

### Log Files
- **Application Log**: `transcription_app.log`
- **Audio Log**: `audio_log.json`

## API Providers Comparison

| Feature | Groq | OpenAI |
|---------|------|--------|
| Model | Whisper Large v3 | Whisper v1 |
| Speed | Faster | Standard |
| Cost | Lower | Higher |
| Availability | High | High |
| Default | ✓ | Alternative |

## Dependencies

Key Python packages:
- `groq` - Groq API client
- `openai` - OpenAI API client
- `sounddevice` - Audio recording
- `numpy` - Audio processing
- `scipy` - Audio file handling
- `tkinter` - GUI framework
- `keyboard` - Global hotkeys
- `pystray` - System tray integration
- `pyautogui` - Auto-paste functionality
- `hanziconv` - Chinese text conversion

## License

This project is for educational and personal use. Please respect the terms of service for OpenAI and Groq APIs.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Verify API key configuration
4. Ensure all dependencies are installed

## Version History

- **v3.2025**: Current version with Groq integration and enhanced features
  - Multi-provider support (Groq + OpenAI)
  - Improved audio processing pipeline
  - Enhanced error handling and retry logic
  - System tray integration
  - Meeting mode for extended recordings