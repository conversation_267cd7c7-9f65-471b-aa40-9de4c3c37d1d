# Telegram Bot Integration

This document explains how to set up and use the Telegram bot integration with your AI Agent Project.

## Overview

The Telegram bot integration provides:

1. **Message Polling**: Continuously polls Telegram for new messages
2. **Duplicate Prevention**: Saves processed message IDs to JSON to avoid replying to the same message twice
3. **Webhook Forwarding**: Forwards original Telegram messages to a configurable webhook URL for AI processing
4. **Reply Handling**: Provides an endpoint for n8n to send AI replies back to Telegram users

## Setup Instructions

### 1. Create a Telegram Bot

1. Open Telegram and search for `@BotFather`
2. Start a conversation and send `/newbot`
3. Follow the instructions to create your bot
4. Save the bot token provided by BotFather

### 2. Configure the Bot

1. Copy `config_local.py.template` to `config_local.py` if you haven't already
2. Edit `config_local.py` and set the following variables:

```python
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN = 'your-actual-bot-token-here'  # From @BotFather
TELEGRAM_WEBHOOK_URL = 'http://localhost:5678/webhook/telegram-messages'  # Your n8n webhook
TELEGRAM_POLLING_INTERVAL = 5  # seconds between polling
TELEGRAM_PROCESSED_MESSAGES_FILE = 'telegram_processed_messages.json'
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Set Up n8n Workflow

Create an n8n workflow with:

1. **Webhook Trigger**: Set to receive POST requests at `/webhook/telegram-messages`
2. **AI Processing**: Process the message with your AI model
3. **HTTP Request Node**: Send the AI reply to `http://localhost:5003/api/telegram-reply`

#### Expected Webhook Payload from Telegram Bot

```json
{
  "message": "User's message text",
  "telegram_message_id": 123,
  "telegram_user_id": 456789,
  "telegram_username": "username",
  "telegram_chat_id": 789012,
  "timestamp": "2024-01-01T12:00:00",
  "source": "telegram_bot",
  "original_message": {
    "message_id": 123,
    "chat_id": 789012,
    "user_id": 456789,
    "username": "username",
    "text": "User's message text",
    "date": "2024-01-01T12:00:00"
  }
}
```

#### Required Reply Payload to Telegram Bot

Send a POST request to `http://localhost:5003/api/telegram-reply` with:

```json
{
  "message": "AI response text",
  "telegram_chat_id": 789012,
  "telegram_message_id": 123  // Optional: for reply threading
}
```

## Usage

### Starting the System

1. Start your Flask application:
```bash
python page.py
```

2. The Telegram bot will automatically start polling if configured correctly

3. Users can now send messages to your Telegram bot

### Message Flow

1. **User sends message** → Telegram bot receives it
2. **Bot checks for duplicates** → Skips if already processed
3. **Bot forwards to webhook** → Sends to your configured `TELEGRAM_WEBHOOK_URL`
4. **n8n processes message** → Your AI workflow handles the message
5. **n8n sends reply** → Posts AI response to `/api/telegram-reply`
6. **Bot sends reply** → User receives AI response in Telegram

### Monitoring

Check the health endpoint for Telegram bot status:
```
GET http://localhost:5003/health
```

Response includes:
```json
{
  "telegram_bot_available": true,
  "telegram_bot_configured": true,
  "telegram_webhook_configured": true,
  "telegram_bot_running": true,
  "telegram_reply_webhook": "http://host.docker.internal:5003/api/telegram-reply"
}
```

## File Structure

- `telegram_bot.py` - Main Telegram bot implementation
- `telegram_processed_messages.json` - Stores processed message IDs (auto-created)
- `config_local.py` - Your local configuration (create from template)

## Troubleshooting

### Bot Not Starting
- Check that `TELEGRAM_BOT_TOKEN` is set correctly
- Verify the token with BotFather
- Check logs for initialization errors

### Messages Not Being Processed
- Verify `TELEGRAM_WEBHOOK_URL` is accessible
- Check n8n webhook is running and configured correctly
- Look for network connectivity issues in logs

### Replies Not Sending
- Ensure `/api/telegram-reply` endpoint is accessible
- Check that `telegram_chat_id` is included in reply payload
- Verify bot has permission to send messages to the chat

### Duplicate Messages
- The system automatically prevents duplicates using `telegram_processed_messages.json`
- If you need to reset, delete this file (bot will restart fresh)

## Security Considerations

1. **Keep your bot token secure** - Never commit it to version control
2. **Use environment variables** for production deployments
3. **Validate webhook URLs** to prevent unauthorized access
4. **Monitor bot usage** to detect abuse

## Advanced Configuration

### Environment Variables

You can also configure using environment variables:

```bash
export TELEGRAM_BOT_TOKEN="your-bot-token"
export TELEGRAM_WEBHOOK_URL="your-webhook-url"
export TELEGRAM_POLLING_INTERVAL="5"
```

### Custom Message Processing

The `telegram_bot.py` module can be extended to:
- Handle different message types (photos, documents, etc.)
- Implement custom filtering logic
- Add user authentication
- Support multiple bots

## API Endpoints

### `/api/telegram-reply` (POST)
Send AI replies back to Telegram users.

**Required fields:**
- `message`: The reply text
- `telegram_chat_id`: Chat ID to send reply to

**Optional fields:**
- `telegram_message_id`: Original message ID for threading

### `/health` (GET)
Check system status including Telegram bot health.

## Support

For issues or questions:
1. Check the logs for error messages
2. Verify your configuration matches this guide
3. Test with a simple message to ensure basic functionality
4. Check n8n workflow is properly configured
