# Question Page - AI Q&A Interface

A standalone Flask application that provides a web interface for asking questions using text or speech input, with integration to ChromaDB and n8n workflows.

## Features

- **Text Input**: Type questions with auto-complete for document sources
- **Speech Input**: Push-to-talk voice recording with Groq Whisper Large v3 transcription
- **Source Filtering**: Use @filename.pdf syntax to filter by specific documents
- **Auto-complete**: Real-time filtering of available document sources
- **n8n Integration**: Send messages to n8n webhook for processing

## Setup Instructions

### 1. Install Dependencies

```bash
cd Question_Page
pip install -r requirements.txt
```

### 2. Configuration

Create a `config_local.py` file with your actual configuration:

```python
# config_local.py
import os

# ChromaDB Configuration (should match your existing setup)
CHROMADB_BASE_URL = "http://localhost:5000"

# n8n Webhook Configuration
# Get this URL from your n8n workflow webhook trigger
WEBHOOK_URL = 'http://localhost:5678/webhook-test/3e04cd5f-9510-4364-b44f-3d2e3f6a572f' # test url
#WEBHOOK_URL = 'http://localhost:5678/webhook/3e04cd5f-9510-4364-b44f-3d2e3f6a572f' # prod url
N8N_WEBHOOK_URL = WEBHOOK_URL

# Groq Configuration
# Get your API key from https://console.groq.com/
GROQ_API_KEY = "gsk_your-groq-api-key-here"
GROQ_MODEL = "whisper-large-v3"

# Flask Configuration
SECRET_KEY = "your-secret-key-for-production"
DEBUG = True
HOST = '0.0.0.0'
PORT = 5001

# Logging Configuration
LOG_LEVEL = 'INFO'
```

### 3. Environment Variables (Alternative)

Instead of `config_local.py`, you can set environment variables:

```bash
export GROQ_API_KEY="gsk_your-groq-api-key-here"
export N8N_WEBHOOK_URL="http://localhost:5678/webhook/your-webhook-id"
export SECRET_KEY="your-secret-key-for-production"
```

### 4. Start the Application

```bash
python page.py
```

The application will be available at: http://localhost:5001

## Usage

### Text Input
1. Type your question in the input field
2. Use @filename.pdf syntax to filter by specific documents
3. Auto-complete will show available documents as you type
4. Press Enter or click Send to submit

### Voice Input
1. Hold down the microphone button
2. Speak your question
3. Release the button to stop recording
4. The speech will be transcribed and added to the text input
5. Edit if needed, then send

### Source Filtering
- Type `@` to see all available documents
- Type `@comp` to see documents starting with "comp"
- Select from dropdown or continue typing
- Example: `@report.pdf What is the revenue for Q3?`

## API Endpoints

- `GET /` - Main page
- `GET /api/sources` - Get available document sources
- `POST /api/speech-to-text` - Convert audio to text
- `POST /api/send-message` - Send message to n8n
- `GET /health` - Health check

## Integration with n8n

The application sends messages to your n8n webhook with this payload:

```json
{
  "message": "User's question text",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "source": "question_page"
}
```

Configure your n8n workflow to:
1. Receive the webhook
2. Process the message (parse @filename syntax if needed)
3. Call the ChromaDB search endpoint
4. Return results to the user (optional)

## Troubleshooting

### Common Issues

1. **Speech-to-text not working**
   - Check if GROQ_API_KEY is set correctly
   - Ensure microphone permissions are granted in browser
   - Check browser console for errors
   - Verify API key format (Groq: starts with `gsk_`)

2. **Auto-complete not showing documents**
   - Ensure ChromaDB server is running on port 5000
   - Check if documents have "Source" metadata field
   - Verify network connectivity

3. **Message sending fails**
   - Check if N8N_WEBHOOK_URL is set correctly
   - Ensure n8n is running and webhook is active
   - Check n8n workflow configuration

### Browser Requirements

- Modern browser with Web Audio API support
- HTTPS required for microphone access (or localhost)
- JavaScript enabled

### Logs

Check the console output for detailed error messages and status information.

## Development

### File Structure
```
Question_Page/
├── page.py              # Main Flask application
├── config.py            # Default configuration
├── config_local.py      # Local configuration (create this)
├── requirements.txt     # Python dependencies
├── templates/
│   └── index.html       # Main page template
├── static/
│   ├── css/
│   │   └── style.css    # Styling
│   └── js/
│       └── app.js       # Frontend JavaScript
└── README.md           # This file
```

### Adding Features

The application is designed to be easily extensible:
- Add new API endpoints in `page.py`
- Modify the UI in `templates/index.html`
- Update styling in `static/css/style.css`
- Add JavaScript functionality in `static/js/app.js`
