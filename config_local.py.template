# Local Configuration Template
# Copy this file to config_local.py and fill in your actual values

import os

# ChromaDB Configuration (should match your existing setup)
CHROMADB_BASE_URL = "http://localhost:5000"

# n8n Webhook Configuration
# Uncomment the URL you want to use
WEBHOOK_URL = 'http://localhost:5678/webhook-test/3e04cd5f-9510-4364-b44f-3d2e3f6a572f' # test url
#WEBHOOK_URL = 'http://localhost:5678/webhook/3e04cd5f-9510-4364-b44f-3d2e3f6a572f' # prod url
N8N_WEBHOOK_URL = os.getenv('N8N_WEBHOOK_URL', WEBHOOK_URL)

# Groq Configuration
# Get your API key from https://console.groq.com/
GROQ_API_KEY = os.getenv('GROQ_API_KEY', "gsk_your-groq-api-key-here")
GROQ_MODEL = "whisper-large-v3"

# Flask Configuration
SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-for-production-change-this')
DEBUG = True
HOST = '0.0.0.0'
PORT = 5001

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN = 'your-telegram-bot-token-here'  # Get from @BotFather on Telegram
TELEGRAM_WEBHOOK_URL = 'http://localhost:5678/webhook/telegram-messages'  # n8n webhook for processing Telegram messages
TELEGRAM_POLLING_INTERVAL = 5  # seconds between polling for new messages
TELEGRAM_PROCESSED_MESSAGES_FILE = 'telegram_processed_messages.json'  # file to store processed message IDs

# Logging Configuration
LOG_LEVEL = 'INFO'
