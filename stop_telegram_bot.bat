@echo off
REM Stop Telegram Bot Script
REM Gracefully stops the running bot process

setlocal enabledelayedexpansion

REM Configuration
set PROJECT_DIR=C:\code\AI_Agent_Project_Improved\Question_Page
set LOG_FILE=%PROJECT_DIR%\startup.log
set PID_FILE=%PROJECT_DIR%\bot.pid

echo [%date% %time%] Stopping Telegram Bot... >> "%LOG_FILE%"

REM Check if PID file exists
if not exist "%PID_FILE%" (
    echo [%date% %time%] No PID file found. Bot may not be running. >> "%LOG_FILE%"
    echo Bot is not running or PID file not found.
    pause
    exit /b 1
)

REM Read PID from file
set /p BOT_PID=<"%PID_FILE%"

REM Check if process is running
tasklist /FI "PID eq %BOT_PID%" 2>nul | find /I "python.exe" >nul
if %errorlevel% neq 0 (
    echo [%date% %time%] Process with PID %BOT_PID% not found. Cleaning up PID file. >> "%LOG_FILE%"
    del "%PID_FILE%" 2>nul
    echo Bot process not found. PID file cleaned up.
    pause
    exit /b 0
)

REM Terminate the process
echo [%date% %time%] Terminating bot process with PID %BOT_PID% >> "%LOG_FILE%"
taskkill /PID %BOT_PID% /F >nul 2>&1

if %errorlevel% equ 0 (
    echo [%date% %time%] Bot stopped successfully >> "%LOG_FILE%"
    del "%PID_FILE%" 2>nul
    echo Bot stopped successfully.
) else (
    echo [%date% %time%] Failed to stop bot process >> "%LOG_FILE%"
    echo Failed to stop bot process.
)

pause
endlocal
