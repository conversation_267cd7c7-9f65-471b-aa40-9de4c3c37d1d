@echo off
REM Advanced Telegram Bot Startup Script
REM Includes logging, error checking, and process management

setlocal enabledelayedexpansion

REM Configuration
set PROJECT_DIR=C:\code\AI_Agent_Project_Improved\Question_Page
set PYTHON_EXE=%PROJECT_DIR%\venv\Scripts\python.exe
set SCRIPT_PATH=%PROJECT_DIR%\page.py
set LOG_FILE=%PROJECT_DIR%\startup.log
set PID_FILE=%PROJECT_DIR%\bot.pid

REM Create log entry
echo [%date% %time%] Starting Telegram Bot... >> "%LOG_FILE%"

REM Check if virtual environment exists
if not exist "%PYTHON_EXE%" (
    echo [%date% %time%] ERROR: Virtual environment not found at %PYTHON_EXE% >> "%LOG_FILE%"
    exit /b 1
)

REM Check if script exists
if not exist "%SCRIPT_PATH%" (
    echo [%date% %time%] ERROR: Script not found at %SCRIPT_PATH% >> "%LOG_FILE%"
    exit /b 1
)

REM Change to project directory
cd /d "%PROJECT_DIR%"

REM Check if bot is already running
if exist "%PID_FILE%" (
    set /p OLD_PID=<"%PID_FILE%"
    tasklist /FI "PID eq !OLD_PID!" 2>nul | find /I "python.exe" >nul
    if !errorlevel! equ 0 (
        echo [%date% %time%] Bot already running with PID !OLD_PID! >> "%LOG_FILE%"
        exit /b 0
    ) else (
        echo [%date% %time%] Removing stale PID file >> "%LOG_FILE%"
        del "%PID_FILE%" 2>nul
    )
)

REM Start the bot and capture PID
echo [%date% %time%] Starting Flask app with Telegram bot... >> "%LOG_FILE%"

REM Start the process in background and get PID
for /f "tokens=2 delims=," %%i in ('wmic process call create "\""%PYTHON_EXE%\"" \""%SCRIPT_PATH%\""" ^| find "ProcessId"') do (
    set PID=%%i
)

REM Clean up PID (remove spaces)
set PID=%PID: =%

REM Save PID to file
echo %PID% > "%PID_FILE%"

echo [%date% %time%] Bot started successfully with PID %PID% >> "%LOG_FILE%"
echo [%date% %time%] Access the app at: http://localhost:5003 >> "%LOG_FILE%"
echo [%date% %time%] Logs are saved to: %LOG_FILE% >> "%LOG_FILE%"

REM Optional: Wait a few seconds to check if process is still running
timeout /t 3 /nobreak >nul
tasklist /FI "PID eq %PID%" 2>nul | find /I "python.exe" >nul
if %errorlevel% equ 0 (
    echo [%date% %time%] Bot is running successfully >> "%LOG_FILE%"
) else (
    echo [%date% %time%] ERROR: Bot failed to start or crashed immediately >> "%LOG_FILE%"
    del "%PID_FILE%" 2>nul
    exit /b 1
)

endlocal
exit /b 0
