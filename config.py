"""
Configuration file for Question Page Flask app.
Copy this file to config_local.py and fill in your actual values.
"""

import os

# ChromaDB Configuration
CHROMADB_BASE_URL = "http://localhost:5000"

# n8n Webhook Configuration for different AI models
# ChatGPT Model Webhook URLs
#CHATGPT_WEBHOOK_URL = 'http://localhost:5678/webhook-test/3e04cd5f-9510-4364-b44f-3d2e3f6a572f' # test url
CHATGPT_WEBHOOK_URL = 'http://localhost:5678/webhook/3e04cd5f-9510-4364-b44f-3d2e3f6a572f' # prod url

# RSJARVIS Model Webhook URLs
#RSJARVIS_WEBHOOK_URL = 'http://localhost:5678/webhook-test/rsjarvis-webhook-id' # test url
RSJARVIS_WEBHOOK_URL = 'http://localhost:5678/webhook/rsjarvis-webhook-id' # prod url

# TELEGRAM Model Webhook URLs
#TELEGRAM_WEBHOOK_URL = 'http://localhost:5678/webhook-test/dc48add0-1df2-4670-837f-71ce101b473a' # test url
TELEGRAM_WEBHOOK_URL = 'http://localhost:5678/webhook/dc48add0-1df2-4670-837f-71ce101b473a' # prod url

# Model Configuration
AI_MODELS = {
    'ChatGPT': {
        'name': 'ChatGPT',
        'webhook_url': os.getenv('CHATGPT_WEBHOOK_URL', CHATGPT_WEBHOOK_URL)
    },
    'RSJARVIS': {
        'name': 'RSJARVIS',
        'webhook_url': os.getenv('RSJARVIS_WEBHOOK_URL', RSJARVIS_WEBHOOK_URL)
    }
}

# Default model
DEFAULT_MODEL = 'ChatGPT'

# Legacy support - use ChatGPT as default
N8N_WEBHOOK_URL = AI_MODELS[DEFAULT_MODEL]['webhook_url']

# Groq Configuration
# Replace with your actual Groq API key
GROQ_API_KEY = os.getenv('GROQ_API_KEY', "********************************************************")
GROQ_MODEL = "whisper-large-v3"

# Flask Configuration
SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here-change-in-production')
DEBUG = True
HOST = '0.0.0.0'
PORT = 5003
# Static asset cache max-age (seconds)
STATIC_MAX_AGE = int(os.getenv('STATIC_MAX_AGE', '3600'))

# HTTPS/SSL Configuration (self-signed supported)
SSL_ENABLED = os.getenv('SSL_ENABLED', 'false').lower() in ('1', 'true', 'yes')
SSL_CERT_PATH = os.getenv('SSL_CERT_PATH', 'certs/cert.pem')
SSL_KEY_PATH = os.getenv('SSL_KEY_PATH', 'certs/key.pem')

# Optional simple login/auth configuration
REQUIRE_LOGIN = os.getenv('REQUIRE_LOGIN', 'false').lower() in ('1', 'true', 'yes')
LOGIN_USERNAME = os.getenv('LOGIN_USERNAME', 'admin')
LOGIN_PASSWORD = os.getenv('LOGIN_PASSWORD', 'admin123')

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '**********************************************')
TELEGRAM_WEBHOOK_URL = os.getenv('TELEGRAM_WEBHOOK_URL', 'http://localhost:5678/webhook/telegram-messages')
TELEGRAM_POLLING_INTERVAL = int(os.getenv('TELEGRAM_POLLING_INTERVAL', '5'))  # seconds
TELEGRAM_PROCESSED_MESSAGES_FILE = os.getenv('TELEGRAM_PROCESSED_MESSAGES_FILE', 'telegram_processed_messages.json')

# Logging Configuration
LOG_LEVEL = 'INFO'
